package com.hngiugu.rocketmq.batch;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

import java.util.ArrayList;
import java.util.List;

public class Producer {
    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String PRODUCER_GROUP = "producer-group";

    public static void main(String[] args) {
        try {
            producer = new DefaultMQProducer();
            producer.setNamesrvAddr(NAME_SRV_ADDRESS);
            producer.setProducerGroup(PRODUCER_GROUP);

            producer.start();

            // 创建一个集合，集合中存放Message
            List<Message> messageList = new ArrayList<>();
            for (int i = 0; i < 100; i++) {
                String body = "Hello world RocketMQ-" + i;
                Message message = new Message("TopicBatch", "TagA-" + i, "OrderID001-" + i,
                        body.getBytes());
                messageList.add(message);
            }

            // 发送消息，返回一个发送结果。发送结果中就有发送确认状态
            SendResult result = producer.send(messageList);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            producer.shutdown();
        }
    }
}
