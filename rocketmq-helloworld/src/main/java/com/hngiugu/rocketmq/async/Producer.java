package com.hngiugu.rocketmq.async;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

import java.util.concurrent.CountDownLatch;


public class Producer {

    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String PRODUCER_GROUP = "producer-group";
    private static CountDownLatch countDownLatch = new CountDownLatch(1);

    public static void main(String[] args) {
        DefaultMQProducer producer = null;
        try {
            producer = new DefaultMQProducer();
            producer.setNamesrvAddr(NAME_SRV_ADDRESS);
            producer.setProducerGroup(PRODUCER_GROUP);

            producer.start();

            // 创建Message
            Message message = new Message("TopicAsync", "TagA", "OrderID001", "Hello world RocketMQ!".getBytes());

            producer.send(message, new SendCallback() {

                /**
                 * 发送成功。确认成功
                 * @param sendResult
                 */
                @Override
                public void onSuccess(SendResult sendResult) {
                    countDownLatch.countDown();
                    System.out.println("成功发送了消息");
                    System.out.println(sendResult);
                }

                @Override
                public void onException(Throwable e) {
                    countDownLatch.countDown();
                    System.out.println("发送失败了");
                    System.out.println(e.getMessage());

                    // 补偿 重发或兜底
                }
            });

            countDownLatch.await();
            System.out.println("消息发送结束了");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            producer.shutdown();
        }
    }
}
